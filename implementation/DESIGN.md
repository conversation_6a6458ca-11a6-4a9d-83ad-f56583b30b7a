# 🎨 MOBILE-FIRST DESIGN PROMPT FÜR GOOGLE AI-TOOLS

**Projekt:** SV FunBall Dortelweil Trainer Portal  
**Zielgruppe:** Google Gemini/Bard AI-Tools  
**Zweck:** Automatisierte Mobile-First-Design-Generierung  
**Version:** 1.0  
**Erstellt:** 2025-01-24  

---

## 📋 PROMPT TEMPLATE FÜR GOOGLE AI-TOOLS

```
Du bist ein erfahrener UI/UX-Designer, der sich auf Mobile-First-Design und moderne Webanwendungen spezialisiert hat. Erstelle ein professionelles, responsives Design für das SV FunBall Dortelweil Trainer Portal basierend auf den folgenden spezifischen Anforderungen:

## 🎯 PROJEKT-KONTEXT

### Organisation
- **Name:** SV FunBall Dortelweil
- **Typ:** Lokaler Sportverein in Bad Vilbel, Deutschland
- **Zielgruppe:** <PERSON><PERSON>, Vereinsmitgli<PERSON>r, Familien
- **Sprache:** Deutsch als Ha<PERSON>tsprache, Englisch als Zweitsprache

### Anwendungstyp
- **Platform:** Laravel 12 Web-Anwendung
- **Authentifizierung:** Magic Link (passwortlos)
- **Rollen:** Member, Trainer, Admin
- **Kern-Features:** Kursanmeldung, Check-in-System, Anwesenheitsverfolgung

## 📱 MOBILE-FIRST DESIGN-PARAMETER

### Responsive Breakpoints
- **Mobile:** 320px - 768px (Primärer Fokus)
- **Tablet:** 768px - 1024px
- **Desktop:** 1024px+ (max-width: 1280px Container)

### Design-Prinzipien
1. **Mobile-First-Ansatz:** Beginne mit 375px Breite (iPhone 13 Mini)
2. **Touch-Friendly:** Mindestens 44px Touch-Targets
3. **Thumb-Navigation:** Wichtige Aktionen im unteren Bildschirmbereich
4. **Progressive Enhancement:** Erweiterte Features für größere Bildschirme

## 🎨 TECHNISCHE DESIGN-ANFORDERUNGEN

### CSS-Framework
- **Basis:** Tailwind CSS 4.0
- **Utility-First:** Verwende Tailwind-Klassen in Designbeschreibungen
- **Custom Properties:** CSS-Variablen für Farbsystem

### Icon-System
- **Primär:** Heroicons (Outline & Solid Varianten)
- **Filament-Kompatibilität:** Für Admin-Bereiche
- **SVG-Format:** Für optimale Performance und Skalierbarkeit

### Typografie
- **Schriftart:** Instrument Sans (Google Fonts)
- **Hierarchie:** text-sm bis text-2xl für Mobile
- **Lesbarkeit:** Mindestens 16px Grundschriftgröße auf Mobile

## 🎨 BRANDING & FARBSCHEMA

### SV FunBall Dortelweil Farbpalette (CSS-Analyse basiert - Exakte Website-Farben)
- **Primär:** #425156 - Haupttext, Überschriften und primäre UI-Elemente
- **Primär-Links:** #cb833a - Links und Hover-States (Orange-Akzent)
- **Primär-Highlight:** #fe8c4a - Logo-Akzent und wichtige Hervorhebungen
- **Sekundär-Orange:** #c6721e - Highlight-Bereiche und sekundäre Akzente
- **Hintergrund-Primär:** #425156 - Body-Hintergrund (Dunkelgrau-Blau)
- **Hintergrund-Sekundär:** #FFFFFF - Content-Bereiche und Karten
- **Hintergrund-Highlight:** #f8ede2 - Highlight-Boxen und besondere Bereiche
- **Tabellen-Alternativ:** #ebf0f1 - Ungerade Tabellenzeilen
- **Input-Hintergrund:** #EBF0F1 - Formulareingaben
- **Border-Standard:** #dadedf - Standardrahmen und Trennlinien
- **Border-Input:** #53676d - Eingabefeld-Rahmen
- **Erfolg:** #008a2a - Erfolgs-Buttons und positive Aktionen
- **Warnung:** #be0027 - Fehler und Warnungen
- **Warnung-Hintergrund:** #ffdac6 - Warnung-Box-Hintergrund
- **Text-Sekundär:** #7e9ca5 - Placeholder-Text und sekundäre Informationen
- **Footer-Text:** #a9bbc1 - Footer-Links und sekundäre Navigation

### Tailwind CSS Äquivalente (Basierend auf exakten Website-Farben)
- **Primär:** `text-slate-600` (#475569 - nächster Tailwind-Wert zu #425156)
- **Links:** `text-orange-600` (#ea580c - nächster Tailwind-Wert zu #cb833a)
- **Highlight:** `text-orange-500` (#f97316 - nächster Tailwind-Wert zu #fe8c4a)
- **Hintergrund:** `bg-slate-600` für Body, `bg-white` für Content
- **Borders:** `border-slate-300` (#cbd5e1 - nächster Tailwind-Wert zu #dadedf)
- **Erfolg:** `bg-green-700` (#15803d - nächster Tailwind-Wert zu #008a2a)
- **Warnung:** `bg-red-700` (#b91c1c - nächster Tailwind-Wert zu #be0027)

### Branding-Richtlinien (CSS-Analyse basiert)
- **Ton:** Professionell, warm und einladend durch Orange-Akzente
- **Stil:** Klassisch-sportlich, übersichtlich, vertrauenswürdig
- **Farbschema:** Grau-Blau Basis (#425156) mit warmen Orange-Akzenten (#cb833a)
- **Charakteristika:** Etablierter Sportverein, Gemeinschaftsgefühl, lokale Tradition
- **Zielgruppe:** Familien, Sportbegeisterte aller Altersgruppen, lokale Gemeinschaft
- **Vermeiden:** Zu moderne/trendy Farben, die von der etablierten Markenidentität abweichen

## 🔧 KOMPONENTEN-SPEZIFIKATIONEN

### Navigation
- **Mobile:** Bottom Tab Bar mit 4-5 Hauptbereichen
- **Desktop:** Sidebar oder Top Navigation
- **Icons:** Heroicons mit deutschen Labels

### Formulare (Exakte Website-Farben)
- **Input-Felder:** `bg-slate-100 border-slate-500 rounded focus:ring-2 focus:ring-orange-600 focus:border-orange-600`
- **Input-Hintergrund:** `bg-slate-100` (#EBF0F1 aus CSS)
- **Input-Border:** `border-slate-500` (#53676d aus CSS)
- **Buttons Primär:** `bg-slate-600 hover:bg-slate-700 text-white` (Hauptaktionen)
- **Buttons Erfolg:** `bg-green-700 hover:bg-green-800 text-white` (#008a2a aus CSS)
- **Buttons Warnung:** `bg-red-700 hover:bg-red-800 text-white` (#be0027 aus CSS)
- **Links:** `text-orange-600 hover:text-orange-700 hover:underline` (#cb833a aus CSS)
- **Validation Erfolg:** `text-green-700 border-green-200 bg-green-50`
- **Validation Fehler:** `text-red-700 border-red-200` mit `bg-orange-100` (#ffdac6 aus CSS)

### Karten/Container (Exakte Website-Farben)
- **Standard:** `bg-white rounded-lg shadow-sm border border-slate-300`
- **Highlight-Box:** `bg-orange-50` (#f8ede2 aus CSS) mit `text-orange-800` (#c6721e aus CSS)
- **Tabellen-Alternativ:** `odd:bg-slate-100` (#ebf0f1 aus CSS) `even:bg-white`
- **Hover:** `hover:shadow-md transition-shadow duration-200`
- **Aktiv:** `border-orange-600 ring-1 ring-orange-600`
- **Spacing:** p-4 auf Mobile, p-6 auf Desktop

## ♿ ACCESSIBILITY-ANFORDERUNGEN (WCAG 2.1)

### Kontrast
- **Text:** Mindestens 4.5:1 Kontrastverhältnis
- **UI-Elemente:** Mindestens 3:1 Kontrastverhältnis
- **Focus-Indikatoren:** Deutlich sichtbare Umrandung

### Navigation
- **Keyboard-Navigation:** Alle interaktiven Elemente erreichbar
- **Screen Reader:** Semantische HTML-Struktur
- **Touch-Targets:** Mindestens 44x44px auf Mobile

## 📋 GEWÜNSCHTE OUTPUT-FORMATE

### Design-Deliverables
1. **Wireframes:** Low-Fidelity Struktur für Mobile (375px)
2. **High-Fidelity Mockups:** Vollständige visuelle Designs
3. **Component Library:** Wiederverwendbare UI-Komponenten
4. **Interactive Prototype:** Klickbare Verbindungen zwischen Screens

### Technische Dokumentation
1. **Tailwind CSS Klassen:** Für jede Komponente dokumentiert
2. **Responsive Breakpoints:** Spezifische Anpassungen pro Bildschirmgröße
3. **State Variations:** Hover, Active, Disabled, Loading States
4. **Laravel Blade Mapping:** Komponentennamen für Entwickler

## 🔄 ITERATIVE VERBESSERUNGS-STRUKTUR

### Feedback-Zyklen
1. **Initial Design:** Erste Mockups zur Bewertung
2. **Revision 1:** Anpassungen basierend auf Feedback
3. **Revision 2:** Feinabstimmung und Finalisierung
4. **Developer Handoff:** Technische Spezifikationen

### Versionierung
- **v1.0:** Basis Mobile-Design
- **v1.1:** Desktop-Anpassungen
- **v1.2:** Accessibility-Verbesserungen
- **v2.0:** Erweiterte Features und Animationen

## 📝 SPEZIFISCHE DESIGN-ANFRAGE

[HIER SPEZIFISCHE SEITE/KOMPONENTE EINFÜGEN]

Beispiel:
"Erstelle ein Mobile-First Design für die Kursanmeldung-Seite mit folgenden Elementen:
- Kursübersicht als Karten-Layout
- Filteroptionen (Datum, Trainer, Schwierigkeitsgrad)
- Anmelde-Button mit Loading-State
- Responsive Anpassung für Desktop"

## 🎯 ERFOLGS-KRITERIEN

### Design-Qualität
- [ ] Mobile-optimierte Benutzerführung
- [ ] Konsistente SV FunBall Dortelweil Branding
- [ ] WCAG 2.1 AA Compliance
- [ ] Tailwind CSS Integration

### Technische Umsetzbarkeit
- [ ] Laravel/Blade-kompatible Struktur
- [ ] Performance-optimierte Assets
- [ ] Skalierbare Komponenten-Architektur
- [ ] Deutsche Lokalisierung berücksichtigt

Bitte erstelle das Design mit detaillierten Erklärungen für jeden Designentscheidung und technische Implementierungshinweise für das Entwicklungsteam.
```

---

## 🔧 VERWENDUNGSANLEITUNG

### 1. Prompt-Anpassung
- Ersetze `[HIER SPEZIFISCHE SEITE/KOMPONENTE EINFÜGEN]` mit konkreter Anfrage
- Füge spezifische Feature-Anforderungen hinzu
- Passe Prioritäten je nach Projektphase an

### 2. Iterative Nutzung
- Verwende für jede neue Seite/Komponente
- Baue auf vorherigen Designs auf
- Referenziere bestehende Komponenten

### 3. Qualitätssicherung
- Überprüfe Branding-Konsistenz
- Validiere Mobile-First-Ansatz
- Teste Accessibility-Compliance

---

## 📚 ERWEITERTE DESIGN-PATTERNS

### Animationen & Mikrointeraktionen
```css
/* Tailwind CSS Animationen für bessere UX */
.fade-in { @apply transition-opacity duration-300 ease-in-out; }
.slide-up { @apply transform transition-transform duration-200 ease-out; }
.button-press { @apply transform active:scale-95 transition-transform duration-75; }
.loading-spinner { @apply animate-spin duration-1000; }
```

### Mobile-Spezifische Patterns
- **Pull-to-Refresh:** Für Kurslisten und Anwesenheitsdaten
- **Swipe-Gestures:** Für Karten-Navigation und Quick-Actions
- **Bottom Sheets:** Für Formulare und Detail-Ansichten
- **Floating Action Button:** Für primäre Aktionen (Kurs anmelden)

### Progressive Web App Features
- **Offline-Modus:** Cached Kursdaten und Anwesenheitslisten
- **Push-Notifications:** Kurs-Erinnerungen und Updates
- **App-Icon:** SV FunBall Dortelweil Logo für Home Screen
- **Splash Screen:** Branded Loading-Screen

## 🎯 SPEZIFISCHE SEITEN-TEMPLATES

### 1. Dashboard (Mobile-First) - Exakte Website-Farben
```
Komponenten:
- Header mit Begrüßung und Notifications-Icon
- Quick-Actions Cards (Nächster Kurs, Check-in, Profil)
- Upcoming Courses List (max. 3 auf Mobile)
- Bottom Navigation (Dashboard, Kurse, Check-in, Profil)

Tailwind Classes (Exakte Website-Farben):
- Container: "px-4 py-6 bg-white min-h-screen"
- Header: "bg-white border-b border-slate-300 text-slate-600"
- Cards: "bg-white rounded-lg shadow-sm border border-slate-300 p-4 mb-4"
- Highlight Cards: "bg-orange-50 border-orange-200 text-orange-800"
- Primary Actions: "bg-slate-600 hover:bg-slate-700 text-white"
- Links: "text-orange-600 hover:text-orange-700 hover:underline"
- Navigation: "bg-white border-t border-slate-300"
- Active Nav: "text-orange-600 bg-orange-50"
- Text Primary: "text-slate-600"
- Text Secondary: "text-slate-400"
```

### 2. Kursanmeldung (Mobile-First) - Exakte Website-Farben
```
Komponenten:
- Search/Filter Bar (sticky top)
- Course Cards mit Trainer-Info und Verfügbarkeit
- Quick-Filter Chips (Heute, Diese Woche, Alle)
- Floating Action Button für "Neuen Kurs vorschlagen"

Tailwind Classes (Exakte Website-Farben):
- Search Bar: "bg-slate-100 border-b border-slate-300 sticky top-0 z-10"
- Search Input: "bg-slate-100 border-slate-500 text-slate-400 placeholder-slate-400"
- Course Cards: "bg-white rounded-lg shadow-sm border border-slate-300 p-4"
- Available: "border-l-4 border-green-700 bg-green-50"
- Full: "border-l-4 border-red-700 bg-orange-100"
- Filter Chips Active: "bg-orange-600 text-white"
- Filter Chips Inactive: "bg-slate-100 text-slate-600 hover:bg-slate-200"
- FAB: "bg-orange-600 hover:bg-orange-700 text-white shadow-lg"
- Course Links: "text-orange-600 hover:text-orange-700 hover:underline"

Responsive Anpassungen:
- Mobile: 1 Spalte, große Touch-Targets (min-h-12)
- Tablet: 2 Spalten, kompaktere Cards
- Desktop: 3 Spalten, Sidebar-Filter
```

### 3. Check-in System (Mobile-First) - Exakte Website-Farben
```
Komponenten:
- QR-Code Scanner Interface
- Manual Check-in Liste
- Status-Anzeige (Anwesend/Abwesend)
- Bulk-Actions für Trainer

Tailwind Classes (Exakte Website-Farben):
- Scanner Frame: "border-2 border-orange-600 rounded-lg bg-white/90"
- Success State: "bg-green-700 text-white" (#008a2a aus CSS)
- Pending State: "bg-orange-600 text-white"
- Error State: "bg-red-700 text-white" (#be0027 aus CSS)
- Warning Background: "bg-orange-100" (#ffdac6 aus CSS)
- Member List: "bg-white border border-slate-300 rounded-lg"
- Checked In: "bg-green-50 border-green-200 text-green-800"
- Not Checked In: "odd:bg-slate-100 even:bg-white border-slate-300 text-slate-600"
- Bulk Actions: "bg-slate-600 hover:bg-slate-700 text-white"
- Action Links: "text-orange-600 hover:text-orange-700 hover:underline"

Spezielle Features:
- Kamera-Integration für QR-Codes
- Offline-Funktionalität
- Real-time Updates
- Haptic Feedback bei erfolgreicher Aktion
```

## 🔍 DESIGN-VALIDIERUNG CHECKLISTE

### Mobile-First Compliance
- [ ] Design beginnt bei 320px Breite
- [ ] Touch-Targets mindestens 44px
- [ ] Wichtige Aktionen im Daumen-Bereich
- [ ] Horizontales Scrollen vermieden
- [ ] Lesbare Schriftgrößen (min. 16px)

### Performance-Optimierung
- [ ] Optimierte Bildgrößen für verschiedene Auflösungen
- [ ] Lazy Loading für Bilder und Komponenten
- [ ] Minimale CSS/JS Bundle-Größe
- [ ] Critical CSS inline für Above-the-Fold Content

### Branding-Konsistenz (CSS-Analyse basiert - Exakte Website-Farben)
- [ ] SV FunBall Dortelweil Farbpalette durchgängig (Grau-Blau mit Orange-Akzenten)
- [ ] Primärfarbe Slate-600 (#425156) für Haupttext und UI-Elemente
- [ ] Orange-600 (#cb833a) für Links und interaktive Elemente
- [ ] Orange-500 (#fe8c4a) für Highlights und wichtige Hervorhebungen
- [ ] Green-700 (#008a2a) für Erfolgs-Aktionen
- [ ] Red-700 (#be0027) für Warnungen und Fehler
- [ ] Konsistente Typografie-Hierarchie (Open Sans als Fallback zu Instrument Sans)
- [ ] Einheitliche Icon-Verwendung (Heroicons)
- [ ] Deutsche Texte und Terminologie
- [ ] Weißer Content-Hintergrund mit Slate-100 (#ebf0f1) für Alternativ-Bereiche
- [ ] Orange-50 (#f8ede2) für Highlight-Boxen und besondere Inhalte

### Accessibility-Compliance
- [ ] Farbkontrast WCAG 2.1 AA konform
- [ ] Keyboard-Navigation vollständig
- [ ] Screen Reader kompatible Struktur
- [ ] Focus-Indikatoren sichtbar

## 🚀 IMPLEMENTIERUNGS-ROADMAP

### Phase 1: Core Mobile Design (Woche 1-2)
- Dashboard Mobile Layout
- Kursanmeldung Mobile Interface
- Basic Check-in System
- Component Library Grundlagen

### Phase 2: Desktop Responsive (Woche 3)
- Desktop-Anpassungen aller Mobile Designs
- Sidebar-Navigation für Desktop
- Erweiterte Filter und Such-Funktionen
- Admin-Panel Integration

### Phase 3: Advanced Features (Woche 4)
- Animationen und Mikrointeraktionen
- PWA-Features (Offline, Push-Notifications)
- Advanced Check-in Features (QR-Code, Bulk)
- Performance-Optimierungen

### Phase 4: Testing & Refinement (Woche 5)
- Cross-Browser Testing
- Accessibility Audit
- Performance Testing
- User Acceptance Testing

---

## 📞 SUPPORT & WEITERENTWICKLUNG

### Design System Maintenance
- **Komponenten-Updates:** Monatliche Review neuer Tailwind Features
- **Branding-Evolution:** Jährliche Überprüfung der Vereins-Ästhetik
- **Accessibility-Updates:** Kontinuierliche WCAG-Compliance-Prüfung

### Feedback-Integration
- **User Testing:** Quartalsweise Usability-Tests mit echten Vereinsmitgliedern
- **Analytics-Integration:** Heatmaps und User Journey Tracking
- **A/B Testing:** Kontinuierliche Optimierung kritischer User Flows

### Skalierbarkeit
- **Neue Features:** Design-Pattern für zukünftige Funktionen
- **Multi-Tenant:** Vorbereitung für andere Sportvereine
- **Internationalisierung:** Erweiterung auf weitere Sprachen

---

---

## 📝 FARBPALETTE ÄNDERUNGSPROTOKOLL

### Version 1.2 - CSS-Analyse der exakten Website-Farben (2025-01-24)

#### **CSS-Datei analysiert:**
- **Quelle:** https://www.fun-ball-dortelweil.de/template/fun-ballV2/css/main.css
- **Methode:** Direkte Extraktion aller Farbwerte aus dem CSS
- **Ergebnis:** 16 exakte Farbwerte identifiziert und kategorisiert

### Version 1.1 - Farbanalyse basierend auf Homepage (2025-01-24)

#### **Durchgeführte Änderungen (Version 1.2 - CSS-basiert):**

**1. Komplette Farbpalette ersetzt basierend auf CSS-Analyse:**
- **Primärfarbe:** #1E40AF → #425156 (Slate-600 ähnlich)
- **Begründung:** Exakte Farbe aus `body, input, textarea` CSS-Regel

**2. Link-Farbe aus CSS extrahiert:**
- **Neu:** #cb833a (Orange-600 ähnlich)
- **Verwendung:** Alle Links und Hover-States
- **CSS-Quelle:** `a { color: #cb833a; }`

**3. Highlight-Farben identifiziert:**
- **Logo-Akzent:** #fe8c4a (Orange-500 ähnlich)
- **Sekundär-Orange:** #c6721e für Highlight-Bereiche
- **CSS-Quelle:** `h2.logo { color: #fe8c4a; }` und `.highlight a { color: #c6721e; }`

**4. Hintergrund-System aus CSS:**
- **Body:** #425156 (Grau-Blau)
- **Content:** #FFFFFF (Weiß)
- **Highlight-Boxen:** #f8ede2 (Orange-50 ähnlich)
- **Tabellen-Alternativ:** #ebf0f1 (Slate-100 ähnlich)

**5. Formular-Farben aus CSS:**
- **Input-Hintergrund:** #EBF0F1
- **Input-Border:** #53676d
- **Placeholder:** #7e9ca5

**6. Funktionale Farben aus CSS:**
- **Erfolg:** #008a2a (Green-700 ähnlich)
- **Warnung:** #be0027 (Red-700 ähnlich)
- **Warnung-Hintergrund:** #ffdac6 (Orange-100 ähnlich)

#### **WCAG 2.1 AA Konformität:**
- Blue-800 auf Weiß: 8.59:1 (AAA) ✅
- Amber-500 auf Weiß: 3.94:1 (AA) ✅
- Emerald-500 auf Weiß: 4.56:1 (AA) ✅
- Red-500 auf Weiß: 5.25:1 (AAA) ✅
- Gray-800 auf Weiß: 12.63:1 (AAA) ✅

#### **Implementierungshinweise:**
- Alle Tailwind CSS Klassen in Templates aktualisiert
- Responsive Design-Patterns beibehalten
- Mobile-First-Ansatz verstärkt durch bessere Touch-Target-Farben

---

**Erstellt für:** SV FunBall Dortelweil Development Team
**Kontakt:** <EMAIL>
**Letzte Aktualisierung:** 2025-01-24
**Version:** 1.1 - Homepage-basierte Farbpalette Integration
