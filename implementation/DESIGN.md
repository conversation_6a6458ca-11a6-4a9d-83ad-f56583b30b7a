# 🎨 MOBILE-FIRST DESIGN PROMPT FÜR GOOGLE AI-TOOLS

**Projekt:** SV FunBall Dortelweil Trainer Portal  
**Zielgruppe:** Google Gemini/Bard AI-Tools  
**Zweck:** Automatisierte Mobile-First-Design-Generierung  
**Version:** 1.0  
**Erstellt:** 2025-01-24  

---

## 📋 PROMPT TEMPLATE FÜR GOOGLE AI-TOOLS

```
Du bist ein erfahrener UI/UX-Designer, der sich auf Mobile-First-Design und moderne Webanwendungen spezialisiert hat. Erstelle ein professionelles, responsives Design für das SV FunBall Dortelweil Trainer Portal basierend auf den folgenden spezifischen Anforderungen:

## 🎯 PROJEKT-KONTEXT

### Organisation
- **Name:** SV FunBall Dortelweil
- **Typ:** Lokaler Sportverein in Bad Vilbel, Deutschland
- **Zielgruppe:** <PERSON><PERSON>, Vereinsmitgli<PERSON>r, Familien
- **Sprache:** Deutsch als Ha<PERSON>tsprache, Englisch als Zweitsprache

### Anwendungstyp
- **Platform:** Laravel 12 Web-Anwendung
- **Authentifizierung:** Magic Link (passwortlos)
- **Rollen:** Member, Trainer, Admin
- **Kern-Features:** Kursanmeldung, Check-in-System, Anwesenheitsverfolgung

## 📱 MOBILE-FIRST DESIGN-PARAMETER

### Responsive Breakpoints
- **Mobile:** 320px - 768px (Primärer Fokus)
- **Tablet:** 768px - 1024px
- **Desktop:** 1024px+ (max-width: 1280px Container)

### Design-Prinzipien
1. **Mobile-First-Ansatz:** Beginne mit 375px Breite (iPhone 13 Mini)
2. **Touch-Friendly:** Mindestens 44px Touch-Targets
3. **Thumb-Navigation:** Wichtige Aktionen im unteren Bildschirmbereich
4. **Progressive Enhancement:** Erweiterte Features für größere Bildschirme

## 🎨 TECHNISCHE DESIGN-ANFORDERUNGEN

### CSS-Framework
- **Basis:** Tailwind CSS 4.0
- **Utility-First:** Verwende Tailwind-Klassen in Designbeschreibungen
- **Custom Properties:** CSS-Variablen für Farbsystem

### Icon-System
- **Primär:** Heroicons (Outline & Solid Varianten)
- **Filament-Kompatibilität:** Für Admin-Bereiche
- **SVG-Format:** Für optimale Performance und Skalierbarkeit

### Typografie
- **Schriftart:** Instrument Sans (Google Fonts)
- **Hierarchie:** text-sm bis text-2xl für Mobile
- **Lesbarkeit:** Mindestens 16px Grundschriftgröße auf Mobile

## 🎨 BRANDING & FARBSCHEMA

### SV FunBall Dortelweil Farbpalette
- **Primär:** #1D4ED8 (Blue-700) - Vereinsfarbe
- **Sekundär:** #059669 (Emerald-600) - Erfolg/Aktiv
- **Akzent:** #DC2626 (Red-600) - Warnungen/Wichtig
- **Neutral:** #374151 (Gray-700) - Text
- **Hintergrund:** #F9FAFB (Gray-50) - App-Hintergrund
- **Weiß:** #FFFFFF - Karten/Container

### Branding-Richtlinien
- **Ton:** Professionell aber freundlich
- **Stil:** Modern, sauber, sportlich
- **Zielgruppe:** Familien und Sportbegeisterte
- **Vermeiden:** Übermäßig technische oder sterile Ästhetik

## 🔧 KOMPONENTEN-SPEZIFIKATIONEN

### Navigation
- **Mobile:** Bottom Tab Bar mit 4-5 Hauptbereichen
- **Desktop:** Sidebar oder Top Navigation
- **Icons:** Heroicons mit deutschen Labels

### Formulare
- **Input-Felder:** Rounded-lg, focus:ring-2, focus:ring-blue-500
- **Buttons:** Primär (bg-blue-600), Sekundär (bg-gray-200)
- **Validation:** Inline-Feedback mit Icons

### Karten/Container
- **Style:** bg-white rounded-lg shadow-sm border border-gray-200
- **Spacing:** p-4 auf Mobile, p-6 auf Desktop
- **Hover-States:** Subtile shadow-md Erhöhung

## ♿ ACCESSIBILITY-ANFORDERUNGEN (WCAG 2.1)

### Kontrast
- **Text:** Mindestens 4.5:1 Kontrastverhältnis
- **UI-Elemente:** Mindestens 3:1 Kontrastverhältnis
- **Focus-Indikatoren:** Deutlich sichtbare Umrandung

### Navigation
- **Keyboard-Navigation:** Alle interaktiven Elemente erreichbar
- **Screen Reader:** Semantische HTML-Struktur
- **Touch-Targets:** Mindestens 44x44px auf Mobile

## 📋 GEWÜNSCHTE OUTPUT-FORMATE

### Design-Deliverables
1. **Wireframes:** Low-Fidelity Struktur für Mobile (375px)
2. **High-Fidelity Mockups:** Vollständige visuelle Designs
3. **Component Library:** Wiederverwendbare UI-Komponenten
4. **Interactive Prototype:** Klickbare Verbindungen zwischen Screens

### Technische Dokumentation
1. **Tailwind CSS Klassen:** Für jede Komponente dokumentiert
2. **Responsive Breakpoints:** Spezifische Anpassungen pro Bildschirmgröße
3. **State Variations:** Hover, Active, Disabled, Loading States
4. **Laravel Blade Mapping:** Komponentennamen für Entwickler

## 🔄 ITERATIVE VERBESSERUNGS-STRUKTUR

### Feedback-Zyklen
1. **Initial Design:** Erste Mockups zur Bewertung
2. **Revision 1:** Anpassungen basierend auf Feedback
3. **Revision 2:** Feinabstimmung und Finalisierung
4. **Developer Handoff:** Technische Spezifikationen

### Versionierung
- **v1.0:** Basis Mobile-Design
- **v1.1:** Desktop-Anpassungen
- **v1.2:** Accessibility-Verbesserungen
- **v2.0:** Erweiterte Features und Animationen

## 📝 SPEZIFISCHE DESIGN-ANFRAGE

[HIER SPEZIFISCHE SEITE/KOMPONENTE EINFÜGEN]

Beispiel:
"Erstelle ein Mobile-First Design für die Kursanmeldung-Seite mit folgenden Elementen:
- Kursübersicht als Karten-Layout
- Filteroptionen (Datum, Trainer, Schwierigkeitsgrad)
- Anmelde-Button mit Loading-State
- Responsive Anpassung für Desktop"

## 🎯 ERFOLGS-KRITERIEN

### Design-Qualität
- [ ] Mobile-optimierte Benutzerführung
- [ ] Konsistente SV FunBall Dortelweil Branding
- [ ] WCAG 2.1 AA Compliance
- [ ] Tailwind CSS Integration

### Technische Umsetzbarkeit
- [ ] Laravel/Blade-kompatible Struktur
- [ ] Performance-optimierte Assets
- [ ] Skalierbare Komponenten-Architektur
- [ ] Deutsche Lokalisierung berücksichtigt

Bitte erstelle das Design mit detaillierten Erklärungen für jeden Designentscheidung und technische Implementierungshinweise für das Entwicklungsteam.
```

---

## 🔧 VERWENDUNGSANLEITUNG

### 1. Prompt-Anpassung
- Ersetze `[HIER SPEZIFISCHE SEITE/KOMPONENTE EINFÜGEN]` mit konkreter Anfrage
- Füge spezifische Feature-Anforderungen hinzu
- Passe Prioritäten je nach Projektphase an

### 2. Iterative Nutzung
- Verwende für jede neue Seite/Komponente
- Baue auf vorherigen Designs auf
- Referenziere bestehende Komponenten

### 3. Qualitätssicherung
- Überprüfe Branding-Konsistenz
- Validiere Mobile-First-Ansatz
- Teste Accessibility-Compliance

---

## 📚 ERWEITERTE DESIGN-PATTERNS

### Animationen & Mikrointeraktionen
```css
/* Tailwind CSS Animationen für bessere UX */
.fade-in { @apply transition-opacity duration-300 ease-in-out; }
.slide-up { @apply transform transition-transform duration-200 ease-out; }
.button-press { @apply transform active:scale-95 transition-transform duration-75; }
.loading-spinner { @apply animate-spin duration-1000; }
```

### Mobile-Spezifische Patterns
- **Pull-to-Refresh:** Für Kurslisten und Anwesenheitsdaten
- **Swipe-Gestures:** Für Karten-Navigation und Quick-Actions
- **Bottom Sheets:** Für Formulare und Detail-Ansichten
- **Floating Action Button:** Für primäre Aktionen (Kurs anmelden)

### Progressive Web App Features
- **Offline-Modus:** Cached Kursdaten und Anwesenheitslisten
- **Push-Notifications:** Kurs-Erinnerungen und Updates
- **App-Icon:** SV FunBall Dortelweil Logo für Home Screen
- **Splash Screen:** Branded Loading-Screen

## 🎯 SPEZIFISCHE SEITEN-TEMPLATES

### 1. Dashboard (Mobile-First)
```
Komponenten:
- Header mit Begrüßung und Notifications-Icon
- Quick-Actions Cards (Nächster Kurs, Check-in, Profil)
- Upcoming Courses List (max. 3 auf Mobile)
- Bottom Navigation (Dashboard, Kurse, Check-in, Profil)

Tailwind Classes:
- Container: "px-4 py-6 bg-gray-50 min-h-screen"
- Cards: "bg-white rounded-lg shadow-sm p-4 mb-4"
- Actions: "grid grid-cols-2 gap-4 mb-6"
```

### 2. Kursanmeldung (Mobile-First)
```
Komponenten:
- Search/Filter Bar (sticky top)
- Course Cards mit Trainer-Info und Verfügbarkeit
- Quick-Filter Chips (Heute, Diese Woche, Alle)
- Floating Action Button für "Neuen Kurs vorschlagen"

Responsive Anpassungen:
- Mobile: 1 Spalte, große Touch-Targets
- Tablet: 2 Spalten, kompaktere Cards
- Desktop: 3 Spalten, Sidebar-Filter
```

### 3. Check-in System (Mobile-First)
```
Komponenten:
- QR-Code Scanner Interface
- Manual Check-in Liste
- Status-Anzeige (Anwesend/Abwesend)
- Bulk-Actions für Trainer

Spezielle Features:
- Kamera-Integration für QR-Codes
- Offline-Funktionalität
- Real-time Updates
- Haptic Feedback bei erfolgreicher Aktion
```

## 🔍 DESIGN-VALIDIERUNG CHECKLISTE

### Mobile-First Compliance
- [ ] Design beginnt bei 320px Breite
- [ ] Touch-Targets mindestens 44px
- [ ] Wichtige Aktionen im Daumen-Bereich
- [ ] Horizontales Scrollen vermieden
- [ ] Lesbare Schriftgrößen (min. 16px)

### Performance-Optimierung
- [ ] Optimierte Bildgrößen für verschiedene Auflösungen
- [ ] Lazy Loading für Bilder und Komponenten
- [ ] Minimale CSS/JS Bundle-Größe
- [ ] Critical CSS inline für Above-the-Fold Content

### Branding-Konsistenz
- [ ] SV FunBall Dortelweil Farbpalette durchgängig
- [ ] Konsistente Typografie-Hierarchie
- [ ] Einheitliche Icon-Verwendung (Heroicons)
- [ ] Deutsche Texte und Terminologie

### Accessibility-Compliance
- [ ] Farbkontrast WCAG 2.1 AA konform
- [ ] Keyboard-Navigation vollständig
- [ ] Screen Reader kompatible Struktur
- [ ] Focus-Indikatoren sichtbar

## 🚀 IMPLEMENTIERUNGS-ROADMAP

### Phase 1: Core Mobile Design (Woche 1-2)
- Dashboard Mobile Layout
- Kursanmeldung Mobile Interface
- Basic Check-in System
- Component Library Grundlagen

### Phase 2: Desktop Responsive (Woche 3)
- Desktop-Anpassungen aller Mobile Designs
- Sidebar-Navigation für Desktop
- Erweiterte Filter und Such-Funktionen
- Admin-Panel Integration

### Phase 3: Advanced Features (Woche 4)
- Animationen und Mikrointeraktionen
- PWA-Features (Offline, Push-Notifications)
- Advanced Check-in Features (QR-Code, Bulk)
- Performance-Optimierungen

### Phase 4: Testing & Refinement (Woche 5)
- Cross-Browser Testing
- Accessibility Audit
- Performance Testing
- User Acceptance Testing

---

## 📞 SUPPORT & WEITERENTWICKLUNG

### Design System Maintenance
- **Komponenten-Updates:** Monatliche Review neuer Tailwind Features
- **Branding-Evolution:** Jährliche Überprüfung der Vereins-Ästhetik
- **Accessibility-Updates:** Kontinuierliche WCAG-Compliance-Prüfung

### Feedback-Integration
- **User Testing:** Quartalsweise Usability-Tests mit echten Vereinsmitgliedern
- **Analytics-Integration:** Heatmaps und User Journey Tracking
- **A/B Testing:** Kontinuierliche Optimierung kritischer User Flows

### Skalierbarkeit
- **Neue Features:** Design-Pattern für zukünftige Funktionen
- **Multi-Tenant:** Vorbereitung für andere Sportvereine
- **Internationalisierung:** Erweiterung auf weitere Sprachen

---

**Erstellt für:** SV FunBall Dortelweil Development Team
**Kontakt:** <EMAIL>
**Letzte Aktualisierung:** 2025-01-24
**Version:** 1.0 - Comprehensive Mobile-First Design Guide
